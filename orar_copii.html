<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title"><PERSON><PERSON><PERSON></title>
    <style>
        :root {
            --primary-color: #4a86e8;
            --secondary-color: #6fa8f5;
            --success-color: #34a853;
            --warning-color: #fbbc04;
            --danger-color: #ea4335;
            --task-color: #e8f4f8;
            --free-time-color: #d9ead3;
            --homework-color: #fff2cc;
            --chores-color: #f4cccc;
            --completed-color: #c8e6c9;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            margin: 10px 0 0 0;
            font-size: 1.1em;
            opacity: 0.9;
        }

        .edit-mode-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            border: 2px solid white;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .edit-mode-toggle:hover {
            background: white;
            color: var(--primary-color);
        }

        .content {
            padding: 30px;
        }

        .schedule-container {
            margin-bottom: 30px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        th, td {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
            position: relative;
        }

        th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
            font-size: 0.95em;
        }

        .time-slot {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            min-width: 100px;
        }

        .activity-cell {
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 50px;
            vertical-align: middle;
        }

        .activity-cell:hover {
            transform: scale(1.02);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .activity-cell.completed {
            background: var(--completed-color) !important;
            text-decoration: line-through;
            opacity: 0.8;
        }

        .activity-cell.completed::after {
            content: "✓";
            position: absolute;
            top: 5px;
            right: 5px;
            color: var(--success-color);
            font-weight: bold;
            font-size: 1.2em;
        }

        .task { background-color: var(--task-color); }
        .free-time { background-color: var(--free-time-color); }
        .homework { background-color: var(--homework-color); }
        .chores { background-color: var(--chores-color); }

        .responsibilities {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
        }

        .responsibilities h2 {
            color: var(--primary-color);
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .responsibility-list {
            list-style: none;
            padding: 0;
        }

        .responsibility-item {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }

        .responsibility-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        }

        .responsibility-item.completed {
            background: var(--completed-color);
            text-decoration: line-through;
            opacity: 0.8;
        }

        .responsibility-checkbox {
            width: 20px;
            height: 20px;
            margin-right: 15px;
            cursor: pointer;
        }

        .edit-mode {
            display: none;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .edit-mode.active {
            display: block;
        }

        .edit-form {
            display: grid;
            gap: 15px;
        }

        .edit-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .edit-tab {
            padding: 10px 20px;
            background: #f8f9fa;
            border: none;
            border-radius: 5px 5px 0 0;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .edit-tab.active {
            background: var(--primary-color);
            color: white;
        }

        .edit-tab:hover {
            background: var(--secondary-color);
            color: white;
        }

        .edit-section {
            display: none;
        }

        .edit-section.active {
            display: block;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #495057;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .schedule-edit-grid {
            display: grid;
            gap: 15px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            background: white;
        }

        .time-slot-editor {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
            position: relative;
        }

        .time-slot-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .time-slot-input {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 1.1em;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 5px 10px;
            background: white;
            min-width: 150px;
        }

        .time-slot-controls {
            display: flex;
            gap: 5px;
            margin-left: auto;
        }

        .btn-time {
            padding: 4px 8px;
            font-size: 0.8em;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-add-time {
            background: var(--success-color);
            color: white;
        }

        .btn-add-time:hover {
            background: #2d8f47;
        }

        .btn-remove-time {
            background: var(--danger-color);
            color: white;
        }

        .btn-remove-time:hover {
            background: #d32f2f;
        }

        .day-activities {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .day-activity-editor {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .day-activity-editor label {
            font-size: 0.9em;
            font-weight: 600;
            color: #6c757d;
        }

        .day-activity-editor input {
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .day-activity-editor select {
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .responsibility-editor {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: white;
            margin-bottom: 10px;
        }

        .responsibility-editor input {
            margin-bottom: 8px;
        }

        .add-remove-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .btn-small {
            padding: 5px 10px;
            font-size: 0.9em;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background: #d32f2f;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #2d8f47;
            transform: translateY(-2px);
        }

        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, var(--success-color), #4caf50);
            height: 100%;
            transition: width 0.5s ease;
            border-radius: 10px;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: 600;
            color: #495057;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .content {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            th, td {
                padding: 8px 4px;
                font-size: 0.9em;
            }

            .edit-mode-toggle {
                position: static;
                margin-top: 15px;
                display: block;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 id="main-title">Orarul Copiilor</h1>
            <p class="subtitle" id="subtitle">Programul zilnic pentru copii</p>
            <button class="edit-mode-toggle" onclick="toggleEditMode()">✏️ Editează</button>
        </div>

        <div class="content">
            <!-- Edit Mode Panel -->
            <div id="edit-mode" class="edit-mode">
                <h3>🛠️ Mod Editare</h3>

                <!-- Edit Tabs -->
                <div class="edit-tabs">
                    <button class="edit-tab active" onclick="switchEditTab('basic')">📝 Setări de Bază</button>
                    <button class="edit-tab" onclick="switchEditTab('times')">⏰ Intervale Orare</button>
                    <button class="edit-tab" onclick="switchEditTab('schedule')">📅 Program Zilnic</button>
                    <button class="edit-tab" onclick="switchEditTab('responsibilities')">🏠 Responsabilități</button>
                </div>

                <!-- Basic Settings Tab -->
                <div id="edit-basic" class="edit-section active">
                    <div class="edit-form">
                        <div class="form-group">
                            <label for="edit-title">Titlu:</label>
                            <input type="text" id="edit-title" placeholder="Orarul Copiilor">
                        </div>
                        <div class="form-group">
                            <label for="edit-subtitle">Subtitlu:</label>
                            <input type="text" id="edit-subtitle" placeholder="Programul zilnic pentru copii">
                        </div>
                        <div class="form-group">
                            <label for="edit-instructions">Instrucțiuni pentru copii:</label>
                            <textarea id="edit-instructions" rows="3" placeholder="Adaugă instrucțiuni speciale aici..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Time Slots Tab -->
                <div id="edit-times" class="edit-section">
                    <h4>⏰ Editează Intervalele Orare</h4>
                    <p style="color: #6c757d; margin-bottom: 15px;">Modifică, adaugă sau șterge intervalele orare pentru program:</p>
                    <div id="time-slots-editor" style="background: white; border: 1px solid #e9ecef; border-radius: 5px; padding: 15px; max-height: 400px; overflow-y: auto;">
                        <!-- Time slots editors will be generated by JavaScript -->
                    </div>
                    <div class="add-remove-buttons" style="margin-top: 15px;">
                        <button class="btn btn-primary btn-small" onclick="addNewTimeSlot()">➕ Adaugă Interval Orar</button>
                        <button class="btn btn-success btn-small" onclick="generateDefaultTimeSlots()">🔄 Resetează la Implicit</button>
                    </div>
                </div>

                <!-- Schedule Tab -->
                <div id="edit-schedule" class="edit-section">
                    <h4>📅 Editează Programul Zilnic</h4>
                    <p style="color: #6c757d; margin-bottom: 15px;">Modifică activitățile pentru fiecare interval orar și zi a săptămânii:</p>
                    <div id="schedule-editor" class="schedule-edit-grid">
                        <!-- Schedule editors will be generated by JavaScript -->
                    </div>
                </div>

                <!-- Responsibilities Tab -->
                <div id="edit-responsibilities" class="edit-section">
                    <h4>🏠 Editează Responsabilitățile</h4>
                    <p style="color: #6c757d; margin-bottom: 15px;">Adaugă, modifică sau șterge responsabilitățile casnice:</p>
                    <div id="responsibilities-editor">
                        <!-- Responsibility editors will be generated by JavaScript -->
                    </div>
                    <div class="add-remove-buttons">
                        <button class="btn btn-primary btn-small" onclick="addNewResponsibility()">➕ Adaugă Responsabilitate</button>
                    </div>
                </div>

                <!-- Save/Cancel Buttons -->
                <div style="margin-top: 20px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                    <div class="add-remove-buttons">
                        <button class="btn btn-primary" onclick="saveAllSettings()">💾 Salvează Toate Modificările</button>
                        <button class="btn btn-success" onclick="resetProgress()">🔄 Resetează Progresul</button>
                        <button class="btn btn-danger" onclick="cancelEdit()">❌ Anulează</button>
                    </div>
                </div>
            </div>

            <!-- Instructions Panel -->
            <div id="instructions-panel" style="background: #e3f2fd; padding: 15px; border-radius: 10px; margin-bottom: 20px; display: none;">
                <h3 style="margin-top: 0; color: var(--primary-color);">📋 Instrucțiuni Speciale</h3>
                <p id="custom-instructions"></p>
            </div>

            <!-- Progress Bar -->
            <div class="progress-container">
                <h3 style="color: var(--primary-color); margin-bottom: 10px;">📊 Progresul de Astăzi</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                </div>
                <div class="progress-text" id="progress-text">0% completat</div>
            </div>

            <!-- Schedule Table -->
            <div class="schedule-container">
                <table id="schedule-table">
                    <thead>
                        <tr>
                            <th>Ora</th>
                            <th>Luni</th>
                            <th>Marți</th>
                            <th>Miercuri</th>
                            <th>Joi</th>
                            <th>Vineri</th>
                            <th>Sâmbătă</th>
                            <th>Duminică</th>
                        </tr>
                    </thead>
                    <tbody id="schedule-body">
                        <!-- Schedule will be generated by JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- Responsibilities Section -->
            <div class="responsibilities">
                <h2>🏠 Responsabilități Casnice</h2>
                <ul class="responsibility-list" id="responsibility-list">
                    <!-- Responsibilities will be generated by JavaScript -->
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 🎯 CONFIGURATION SYSTEM - Easy to customize!
        const CONFIG = {
            // Basic Settings
            title: "Orarul Copiilor",
            subtitle: "Programul zilnic pentru copii",
            language: "ro", // ro, en, etc.

            // Custom Instructions (shown when not empty)
            customInstructions: "",

            // Time slots for the schedule
            timeSlots: [
                "7:00 - 8:00",
                "8:00 - 9:00",
                "9:00 - 10:00",
                "10:00 - 11:00",
                "11:00 - 12:00",
                "12:00 - 13:00",
                "13:00 - 14:00",
                "14:00 - 15:00",
                "15:00 - 16:00",
                "16:00 - 17:00",
                "17:00 - 18:00",
                "18:00 - 19:00",
                "19:00 - 20:00",
                "20:00 - 21:00"
            ],

            // Days of the week
            days: ["Luni", "Marți", "Miercuri", "Joi", "Vineri", "Sâmbătă", "Duminică"],

            // Schedule activities - EASY TO CUSTOMIZE!
            schedule: {
                "7:00 - 8:00": {
                    "Luni": { text: "Trezire, Micul dejun", type: "task" },
                    "Marți": { text: "Trezire, Micul dejun", type: "task" },
                    "Miercuri": { text: "Trezire, Micul dejun", type: "task" },
                    "Joi": { text: "Trezire, Micul dejun", type: "task" },
                    "Vineri": { text: "Trezire, Micul dejun", type: "task" },
                    "Sâmbătă": { text: "Somn prelungit", type: "free-time" },
                    "Duminică": { text: "Somn prelungit", type: "free-time" }
                },
                "8:00 - 9:00": {
                    "Luni": { text: "Pregătire pentru școală", type: "task" },
                    "Marți": { text: "Pregătire pentru școală", type: "task" },
                    "Miercuri": { text: "Pregătire pentru școală", type: "task" },
                    "Joi": { text: "Pregătire pentru școală", type: "task" },
                    "Vineri": { text: "Pregătire pentru școală", type: "task" },
                    "Sâmbătă": { text: "Micul dejun", type: "task" },
                    "Duminică": { text: "Micul dejun", type: "task" }
                },
                "9:00 - 10:00": {
                    "Luni": { text: "Școala", type: "homework" },
                    "Marți": { text: "Școala", type: "homework" },
                    "Miercuri": { text: "Școala", type: "homework" },
                    "Joi": { text: "Școala", type: "homework" },
                    "Vineri": { text: "Școala", type: "homework" },
                    "Sâmbătă": { text: "Timp liber", type: "free-time" },
                    "Duminică": { text: "Timp liber", type: "free-time" }
                },
                "12:00 - 13:00": {
                    "Luni": { text: "Prânz", type: "task" },
                    "Marți": { text: "Prânz", type: "task" },
                    "Miercuri": { text: "Prânz", type: "task" },
                    "Joi": { text: "Prânz", type: "task" },
                    "Vineri": { text: "Prânz", type: "task" },
                    "Sâmbătă": { text: "Prânz", type: "task" },
                    "Duminică": { text: "Prânz", type: "task" }
                },
                "15:00 - 16:00": {
                    "Luni": { text: "Teme", type: "homework" },
                    "Marți": { text: "Teme", type: "homework" },
                    "Miercuri": { text: "Teme", type: "homework" },
                    "Joi": { text: "Teme", type: "homework" },
                    "Vineri": { text: "Timp liber", type: "free-time" },
                    "Sâmbătă": { text: "Activități", type: "free-time" },
                    "Duminică": { text: "Timp liber", type: "free-time" }
                },
                "17:00 - 18:00": {
                    "Luni": { text: "Treburi casnice", type: "chores" },
                    "Marți": { text: "Treburi casnice", type: "chores" },
                    "Miercuri": { text: "Treburi casnice", type: "chores" },
                    "Joi": { text: "Treburi casnice", type: "chores" },
                    "Vineri": { text: "Treburi casnice", type: "chores" },
                    "Sâmbătă": { text: "Treburi casnice", type: "chores" },
                    "Duminică": { text: "Pregătire săptămână", type: "chores" }
                },
                "19:00 - 20:00": {
                    "Luni": { text: "Cină", type: "task" },
                    "Marți": { text: "Cină", type: "task" },
                    "Miercuri": { text: "Cină", type: "task" },
                    "Joi": { text: "Cină", type: "task" },
                    "Vineri": { text: "Cină", type: "task" },
                    "Sâmbătă": { text: "Cină", type: "task" },
                    "Duminică": { text: "Cină", type: "task" }
                },
                "20:00 - 21:00": {
                    "Luni": { text: "Pregătire pentru culcare", type: "task" },
                    "Marți": { text: "Pregătire pentru culcare", type: "task" },
                    "Miercuri": { text: "Pregătire pentru culcare", type: "task" },
                    "Joi": { text: "Pregătire pentru culcare", type: "task" },
                    "Vineri": { text: "Timp liber", type: "free-time" },
                    "Sâmbătă": { text: "Timp liber", type: "free-time" },
                    "Duminică": { text: "Pregătire pentru culcare", type: "task" }
                }
            },

            // Household responsibilities - EASY TO CUSTOMIZE!
            responsibilities: [
                { text: "Aranjarea patului", frequency: "zilnic", type: "daily" },
                { text: "Strângerea jucăriilor", frequency: "zilnic", type: "daily" },
                { text: "Ajutor la masă", frequency: "conform programului", type: "scheduled" },
                { text: "Hrănirea animalelor de companie", frequency: "zilnic", type: "daily" },
                { text: "Sortarea hainelor murdare", frequency: "săptămânal", type: "weekly" },
                { text: "Curățarea camerei", frequency: "săptămânal", type: "weekly" },
                { text: "Ajutor la cumpărături", frequency: "la nevoie", type: "asneeded" }
            ],

            // Activity types and their colors
            activityTypes: {
                "task": { name: "Activități de bază", color: "#e8f4f8" },
                "free-time": { name: "Timp liber", color: "#d9ead3" },
                "homework": { name: "Școală/Teme", color: "#fff2cc" },
                "chores": { name: "Treburi casnice", color: "#f4cccc" }
            }
        };

        // 🔧 CORE FUNCTIONALITY
        let completedActivities = new Set();
        let completedResponsibilities = new Set();

        // Initialize the application
        function initApp() {
            loadSettings();
            generateSchedule();
            generateResponsibilities();
            updateProgress();
            loadProgress();
        }

        // Generate the schedule table
        function generateSchedule() {
            const tbody = document.getElementById('schedule-body');
            tbody.innerHTML = '';

            CONFIG.timeSlots.forEach(timeSlot => {
                const row = document.createElement('tr');

                // Time slot cell
                const timeCell = document.createElement('td');
                timeCell.className = 'time-slot';
                timeCell.textContent = timeSlot;
                row.appendChild(timeCell);

                // Activity cells for each day
                CONFIG.days.forEach(day => {
                    const cell = document.createElement('td');
                    cell.className = 'activity-cell';

                    const activity = CONFIG.schedule[timeSlot] && CONFIG.schedule[timeSlot][day];
                    if (activity) {
                        cell.textContent = activity.text;
                        cell.classList.add(activity.type);
                        cell.onclick = () => toggleActivityCompletion(timeSlot, day, cell);

                        // Check if already completed
                        const activityId = `${timeSlot}-${day}`;
                        if (completedActivities.has(activityId)) {
                            cell.classList.add('completed');
                        }
                    } else {
                        cell.textContent = '-';
                        cell.style.background = '#f8f9fa';
                    }

                    row.appendChild(cell);
                });

                tbody.appendChild(row);
            });
        }

        // Generate responsibilities list
        function generateResponsibilities() {
            const list = document.getElementById('responsibility-list');
            list.innerHTML = '';

            CONFIG.responsibilities.forEach((responsibility, index) => {
                const item = document.createElement('li');
                item.className = 'responsibility-item';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.className = 'responsibility-checkbox';
                checkbox.id = `resp-${index}`;
                checkbox.onchange = () => toggleResponsibilityCompletion(index, item);

                const label = document.createElement('label');
                label.htmlFor = `resp-${index}`;
                label.innerHTML = `<strong>${responsibility.text}</strong> - ${responsibility.frequency}`;

                // Check if already completed
                if (completedResponsibilities.has(index.toString())) {
                    checkbox.checked = true;
                    item.classList.add('completed');
                }

                item.appendChild(checkbox);
                item.appendChild(label);
                list.appendChild(item);
            });
        }

        // Toggle activity completion
        function toggleActivityCompletion(timeSlot, day, cell) {
            const activityId = `${timeSlot}-${day}`;

            if (completedActivities.has(activityId)) {
                completedActivities.delete(activityId);
                cell.classList.remove('completed');
            } else {
                completedActivities.add(activityId);
                cell.classList.add('completed');
            }

            updateProgress();
            saveProgress();
        }

        // Toggle responsibility completion
        function toggleResponsibilityCompletion(index, item) {
            const respId = index.toString();

            if (completedResponsibilities.has(respId)) {
                completedResponsibilities.delete(respId);
                item.classList.remove('completed');
            } else {
                completedResponsibilities.add(respId);
                item.classList.add('completed');
            }

            updateProgress();
            saveProgress();
        }

        // Update progress bar
        function updateProgress() {
            const totalActivities = Object.keys(CONFIG.schedule).length * CONFIG.days.length;
            const totalResponsibilities = CONFIG.responsibilities.length;
            const total = totalActivities + totalResponsibilities;

            const completed = completedActivities.size + completedResponsibilities.size;
            const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');

            progressFill.style.width = percentage + '%';
            progressText.textContent = `${percentage}% completat (${completed}/${total} activități)`;
        }

        // Save progress to localStorage
        function saveProgress() {
            const progress = {
                activities: Array.from(completedActivities),
                responsibilities: Array.from(completedResponsibilities),
                date: new Date().toDateString()
            };
            localStorage.setItem('childScheduleProgress', JSON.stringify(progress));
        }

        // Load progress from localStorage
        function loadProgress() {
            const saved = localStorage.getItem('childScheduleProgress');
            if (saved) {
                const progress = JSON.parse(saved);
                const today = new Date().toDateString();

                // Only load if it's from today
                if (progress.date === today) {
                    completedActivities = new Set(progress.activities);
                    completedResponsibilities = new Set(progress.responsibilities);

                    // Update UI
                    generateSchedule();
                    generateResponsibilities();
                    updateProgress();
                }
            }
        }

        // Reset progress
        function resetProgress() {
            if (confirm('Sigur vrei să resetezi tot progresul de astăzi?')) {
                completedActivities.clear();
                completedResponsibilities.clear();
                localStorage.removeItem('childScheduleProgress');

                generateSchedule();
                generateResponsibilities();
                updateProgress();

                alert('Progresul a fost resetat!');
            }
        }

        // Toggle edit mode
        function toggleEditMode() {
            const editMode = document.getElementById('edit-mode');
            const isActive = editMode.classList.contains('active');

            if (isActive) {
                editMode.classList.remove('active');
            } else {
                editMode.classList.add('active');
                loadCurrentSettings();
                generateTimeSlotsEditor();
                generateScheduleEditor();
                generateResponsibilitiesEditor();
            }
        }

        // Switch between edit tabs
        function switchEditTab(tabName) {
            // Remove active class from all tabs and sections
            document.querySelectorAll('.edit-tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.edit-section').forEach(section => section.classList.remove('active'));

            // Add active class to selected tab and section
            event.target.classList.add('active');
            document.getElementById(`edit-${tabName}`).classList.add('active');
        }

        // Load current settings into edit form
        function loadCurrentSettings() {
            document.getElementById('edit-title').value = CONFIG.title;
            document.getElementById('edit-subtitle').value = CONFIG.subtitle;
            document.getElementById('edit-instructions').value = CONFIG.customInstructions;
        }

        // Generate time slots editor
        function generateTimeSlotsEditor() {
            const container = document.getElementById('time-slots-editor');
            container.innerHTML = '';

            CONFIG.timeSlots.forEach((timeSlot, index) => {
                const timeSlotDiv = document.createElement('div');
                timeSlotDiv.style.cssText = 'display: flex; align-items: center; gap: 10px; margin-bottom: 10px; padding: 10px; border: 1px solid #dee2e6; border-radius: 5px; background: #f8f9fa;';

                const input = document.createElement('input');
                input.type = 'text';
                input.value = timeSlot;
                input.id = `time-slot-${index}`;
                input.placeholder = 'ex: 7:00 - 8:00';
                input.style.cssText = 'flex: 1; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; font-weight: 600;';
                timeSlotDiv.appendChild(input);

                const moveUpBtn = document.createElement('button');
                moveUpBtn.className = 'btn-time btn-add-time';
                moveUpBtn.textContent = '↑';
                moveUpBtn.title = 'Mută în sus';
                moveUpBtn.onclick = () => moveTimeSlot(index, -1);
                moveUpBtn.disabled = index === 0;
                timeSlotDiv.appendChild(moveUpBtn);

                const moveDownBtn = document.createElement('button');
                moveDownBtn.className = 'btn-time btn-add-time';
                moveDownBtn.textContent = '↓';
                moveDownBtn.title = 'Mută în jos';
                moveDownBtn.onclick = () => moveTimeSlot(index, 1);
                moveDownBtn.disabled = index === CONFIG.timeSlots.length - 1;
                timeSlotDiv.appendChild(moveDownBtn);

                const removeBtn = document.createElement('button');
                removeBtn.className = 'btn-time btn-remove-time';
                removeBtn.textContent = '🗑️';
                removeBtn.title = 'Șterge interval';
                removeBtn.onclick = () => removeTimeSlot(index);
                timeSlotDiv.appendChild(removeBtn);

                container.appendChild(timeSlotDiv);
            });
        }

        // Add new time slot
        function addNewTimeSlot() {
            const newTime = prompt('Introdu noul interval orar (ex: 21:00 - 22:00):');
            if (newTime && newTime.trim()) {
                CONFIG.timeSlots.push(newTime.trim());
                generateTimeSlotsEditor();
            }
        }

        // Remove time slot
        function removeTimeSlot(index) {
            if (CONFIG.timeSlots.length <= 1) {
                alert('Nu poți șterge ultimul interval orar!');
                return;
            }

            if (confirm('Sigur vrei să ștergi acest interval orar? Toate activitățile asociate se vor pierde.')) {
                const removedTimeSlot = CONFIG.timeSlots[index];
                CONFIG.timeSlots.splice(index, 1);

                // Remove associated schedule entries
                delete CONFIG.schedule[removedTimeSlot];

                generateTimeSlotsEditor();
            }
        }

        // Move time slot up or down
        function moveTimeSlot(index, direction) {
            const newIndex = index + direction;
            if (newIndex >= 0 && newIndex < CONFIG.timeSlots.length) {
                // Swap time slots
                [CONFIG.timeSlots[index], CONFIG.timeSlots[newIndex]] = [CONFIG.timeSlots[newIndex], CONFIG.timeSlots[index]];
                generateTimeSlotsEditor();
            }
        }

        // Generate default time slots
        function generateDefaultTimeSlots() {
            if (confirm('Sigur vrei să resetezi intervalele orare la valorile implicite? Toate modificările se vor pierde.')) {
                CONFIG.timeSlots = [
                    "7:00 - 8:00",
                    "8:00 - 9:00",
                    "9:00 - 10:00",
                    "10:00 - 11:00",
                    "11:00 - 12:00",
                    "12:00 - 13:00",
                    "13:00 - 14:00",
                    "14:00 - 15:00",
                    "15:00 - 16:00",
                    "16:00 - 17:00",
                    "17:00 - 18:00",
                    "18:00 - 19:00",
                    "19:00 - 20:00",
                    "20:00 - 21:00"
                ];
                generateTimeSlotsEditor();
            }
        }

        // Generate schedule editor
        function generateScheduleEditor() {
            const container = document.getElementById('schedule-editor');
            container.innerHTML = '';

            CONFIG.timeSlots.forEach(timeSlot => {
                const timeSlotDiv = document.createElement('div');
                timeSlotDiv.className = 'time-slot-editor';

                const header = document.createElement('div');
                header.className = 'time-slot-header';
                header.textContent = timeSlot;
                timeSlotDiv.appendChild(header);

                const dayActivities = document.createElement('div');
                dayActivities.className = 'day-activities';

                CONFIG.days.forEach(day => {
                    const dayEditor = document.createElement('div');
                    dayEditor.className = 'day-activity-editor';

                    const label = document.createElement('label');
                    label.textContent = day;
                    dayEditor.appendChild(label);

                    const textInput = document.createElement('input');
                    textInput.type = 'text';
                    textInput.placeholder = 'Activitate...';
                    textInput.id = `schedule-${timeSlot}-${day}-text`;

                    const activity = CONFIG.schedule[timeSlot] && CONFIG.schedule[timeSlot][day];
                    if (activity) {
                        textInput.value = activity.text;
                    }
                    dayEditor.appendChild(textInput);

                    const typeSelect = document.createElement('select');
                    typeSelect.id = `schedule-${timeSlot}-${day}-type`;

                    Object.keys(CONFIG.activityTypes).forEach(type => {
                        const option = document.createElement('option');
                        option.value = type;
                        option.textContent = CONFIG.activityTypes[type].name;
                        if (activity && activity.type === type) {
                            option.selected = true;
                        }
                        typeSelect.appendChild(option);
                    });
                    dayEditor.appendChild(typeSelect);

                    dayActivities.appendChild(dayEditor);
                });

                timeSlotDiv.appendChild(dayActivities);
                container.appendChild(timeSlotDiv);
            });
        }

        // Generate responsibilities editor
        function generateResponsibilitiesEditor() {
            const container = document.getElementById('responsibilities-editor');
            container.innerHTML = '';

            CONFIG.responsibilities.forEach((responsibility, index) => {
                const respDiv = document.createElement('div');
                respDiv.className = 'responsibility-editor';

                const textInput = document.createElement('input');
                textInput.type = 'text';
                textInput.placeholder = 'Responsabilitate...';
                textInput.value = responsibility.text;
                textInput.id = `resp-text-${index}`;
                respDiv.appendChild(textInput);

                const freqInput = document.createElement('input');
                freqInput.type = 'text';
                freqInput.placeholder = 'Frecvența (ex: zilnic, săptămânal)...';
                freqInput.value = responsibility.frequency;
                freqInput.id = `resp-freq-${index}`;
                respDiv.appendChild(freqInput);

                const typeSelect = document.createElement('select');
                typeSelect.id = `resp-type-${index}`;
                ['daily', 'weekly', 'scheduled', 'asneeded'].forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    if (responsibility.type === type) {
                        option.selected = true;
                    }
                    typeSelect.appendChild(option);
                });
                respDiv.appendChild(typeSelect);

                const removeBtn = document.createElement('button');
                removeBtn.className = 'btn btn-danger btn-small';
                removeBtn.textContent = '🗑️ Șterge';
                removeBtn.onclick = () => removeResponsibility(index);
                respDiv.appendChild(removeBtn);

                container.appendChild(respDiv);
            });
        }

        // Add new responsibility
        function addNewResponsibility() {
            CONFIG.responsibilities.push({
                text: 'Responsabilitate nouă',
                frequency: 'zilnic',
                type: 'daily'
            });
            generateResponsibilitiesEditor();
        }

        // Remove responsibility
        function removeResponsibility(index) {
            if (confirm('Sigur vrei să ștergi această responsabilitate?')) {
                CONFIG.responsibilities.splice(index, 1);
                generateResponsibilitiesEditor();
            }
        }

        // Save all settings
        function saveAllSettings() {
            // Save basic settings
            CONFIG.title = document.getElementById('edit-title').value;
            CONFIG.subtitle = document.getElementById('edit-subtitle').value;
            CONFIG.customInstructions = document.getElementById('edit-instructions').value;

            // Save schedule changes
            CONFIG.timeSlots.forEach(timeSlot => {
                CONFIG.days.forEach(day => {
                    const textInput = document.getElementById(`schedule-${timeSlot}-${day}-text`);
                    const typeSelect = document.getElementById(`schedule-${timeSlot}-${day}-type`);

                    if (textInput && typeSelect) {
                        if (!CONFIG.schedule[timeSlot]) {
                            CONFIG.schedule[timeSlot] = {};
                        }

                        if (textInput.value.trim()) {
                            CONFIG.schedule[timeSlot][day] = {
                                text: textInput.value.trim(),
                                type: typeSelect.value
                            };
                        } else {
                            delete CONFIG.schedule[timeSlot][day];
                        }
                    }
                });
            });

            // Save responsibilities changes
            CONFIG.responsibilities = [];
            const respEditors = document.querySelectorAll('.responsibility-editor');
            respEditors.forEach((editor, index) => {
                const textInput = document.getElementById(`resp-text-${index}`);
                const freqInput = document.getElementById(`resp-freq-${index}`);
                const typeSelect = document.getElementById(`resp-type-${index}`);

                if (textInput && textInput.value.trim()) {
                    CONFIG.responsibilities.push({
                        text: textInput.value.trim(),
                        frequency: freqInput.value.trim() || 'zilnic',
                        type: typeSelect.value
                    });
                }
            });

            // Update UI
            document.getElementById('main-title').textContent = CONFIG.title;
            document.getElementById('subtitle').textContent = CONFIG.subtitle;
            document.getElementById('page-title').textContent = CONFIG.title;

            // Show/hide instructions panel
            const instructionsPanel = document.getElementById('instructions-panel');
            const customInstructions = document.getElementById('custom-instructions');

            if (CONFIG.customInstructions.trim()) {
                customInstructions.textContent = CONFIG.customInstructions;
                instructionsPanel.style.display = 'block';
            } else {
                instructionsPanel.style.display = 'none';
            }

            // Regenerate schedule and responsibilities
            generateSchedule();
            generateResponsibilities();

            // Save to localStorage
            localStorage.setItem('childScheduleConfig', JSON.stringify(CONFIG));

            // Hide edit mode
            document.getElementById('edit-mode').classList.remove('active');

            alert('Toate setările au fost salvate!');
        }

        // Cancel edit
        function cancelEdit() {
            if (confirm('Sigur vrei să anulezi modificările? Toate schimbările nesalvate se vor pierde.')) {
                document.getElementById('edit-mode').classList.remove('active');
            }
        }

        // Load settings from localStorage
        function loadSettings() {
            const saved = localStorage.getItem('childScheduleConfig');
            if (saved) {
                const savedConfig = JSON.parse(saved);
                Object.assign(CONFIG, savedConfig);

                // Update UI
                document.getElementById('main-title').textContent = CONFIG.title;
                document.getElementById('subtitle').textContent = CONFIG.subtitle;
                document.getElementById('page-title').textContent = CONFIG.title;

                // Show instructions if available
                if (CONFIG.customInstructions.trim()) {
                    document.getElementById('custom-instructions').textContent = CONFIG.customInstructions;
                    document.getElementById('instructions-panel').style.display = 'block';
                }
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initApp);
    </script>
</body>
</html>